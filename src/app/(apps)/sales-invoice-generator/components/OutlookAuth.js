import { motion } from "framer-motion";
import { useState, useEffect } from "react";
import { toast } from "react-hot-toast";
import { TbArrowLeft, TbMail, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, TbSend } from "react-icons/tb";
import { useMsal } from "@azure/msal-react";
import { graphLoginRequest } from "@/lib/msal/config";
import { API_ENDPOINTS } from "@/config/api";
import api from "@/services/api";

const OutlookAuth = ({ onBack, variants }) => {
  const { instance, accounts } = useMsal();
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userAccounts, setUserAccounts] = useState([]);
  const [selectedAccount, setSelectedAccount] = useState(null);
  const [loading, setLoading] = useState(false);
  const [sendingTest, setSendingTest] = useState(false);
  const [currentStep, setCurrentStep] = useState('auth'); // auth, accounts, test

  useEffect(() => {
    // Check if user is already authenticated
    if (accounts.length > 0) {
      setIsAuthenticated(true);
      setUserAccounts(accounts);
      setCurrentStep('accounts');
    }
  }, [accounts]);

  const handleLogin = async () => {
    setLoading(true);
    try {
      const loginResponse = await instance.loginPopup(graphLoginRequest);
      console.log('Login successful:', loginResponse);
      
      setIsAuthenticated(true);
      setUserAccounts([loginResponse.account]);
      setCurrentStep('accounts');
      toast.success('Successfully connected to Microsoft Outlook!');
    } catch (error) {
      console.error('Login failed:', error);
      toast.error('Failed to connect to Microsoft Outlook. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAccountSelect = (account) => {
    setSelectedAccount(account);
    setCurrentStep('test');
  };

  const handleSendTestEmail = async () => {
    if (!selectedAccount) {
      toast.error('Please select an account first');
      return;
    }

    setSendingTest(true);
    try {
      // Get access token for the selected account
      const tokenRequest = {
        ...graphLoginRequest,
        account: selectedAccount,
      };

      const tokenResponse = await instance.acquireTokenSilent(tokenRequest);
      
      // Send test email via backend
      const response = await api.post(API_ENDPOINTS.MS_GRAPH_SEND_TEST_EMAIL, {
        access_token: tokenResponse.accessToken,
        from_email: selectedAccount.username,
        account_info: {
          name: selectedAccount.name,
          username: selectedAccount.username,
          homeAccountId: selectedAccount.homeAccountId
        }
      });

      if (response.data.success) {
        toast.success('Test email sent successfully!');
        
        // Save the configuration
        await api.post(API_ENDPOINTS.MS_GRAPH_SAVE_CONFIG, {
          account_info: {
            name: selectedAccount.name,
            username: selectedAccount.username,
            homeAccountId: selectedAccount.homeAccountId
          },
          provider: 'outlook'
        });
        
        toast.success('Email configuration saved!');
      } else {
        throw new Error(response.data.error || 'Failed to send test email');
      }
    } catch (error) {
      console.error('Test email failed:', error);
      toast.error(`Failed to send test email: ${error.message}`);
    } finally {
      setSendingTest(false);
    }
  };

  const renderAuthStep = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <div className="text-center">
        <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
          <TbMail className="h-8 w-8 text-blue-600" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Connect Your Microsoft Outlook Account
        </h3>
        <p className="text-gray-600 mb-6">
          Authorize MizuFlow to send emails on your behalf using Microsoft Graph API
        </p>
        
        <button
          onClick={handleLogin}
          disabled={loading}
          className="flex items-center gap-2 px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mx-auto"
        >
          {loading ? (
            <>
              <TbLoader className="h-5 w-5 animate-spin" />
              Connecting...
            </>
          ) : (
            <>
              <TbMail className="h-5 w-5" />
              Connect Outlook Account
            </>
          )}
        </button>
      </div>
    </div>
  );

  const renderAccountsStep = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Select Account for Sending Emails
      </h3>
      
      <div className="space-y-3">
        {userAccounts.map((account, index) => (
          <motion.div
            key={account.homeAccountId || index}
            className={`
              p-4 border-2 rounded-lg cursor-pointer transition-all
              ${selectedAccount?.homeAccountId === account.homeAccountId 
                ? 'border-blue-500 bg-blue-50' 
                : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50'
              }
            `}
            onClick={() => handleAccountSelect(account)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-gray-900">{account.name}</p>
                <p className="text-sm text-gray-600">{account.username}</p>
              </div>
              <div className={`
                w-6 h-6 rounded-full border-2 flex items-center justify-center
                ${selectedAccount?.homeAccountId === account.homeAccountId 
                  ? 'border-blue-500 bg-blue-500' 
                  : 'border-gray-300'
                }
              `}>
                {selectedAccount?.homeAccountId === account.homeAccountId && (
                  <TbCheck className="h-4 w-4 text-white" />
                )}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {selectedAccount && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <button
            onClick={() => setCurrentStep('test')}
            className="w-full px-4 py-2 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 transition-colors"
          >
            Continue with Selected Account
          </button>
        </div>
      )}
    </div>
  );

  const renderTestStep = () => (
    <div className="bg-white border border-gray-200 rounded-lg p-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4">
        Test Email Configuration
      </h3>
      
      {selectedAccount && (
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <p className="text-sm text-gray-600 mb-1">Sending from:</p>
          <p className="font-medium text-gray-900">{selectedAccount.name}</p>
          <p className="text-sm text-gray-600">{selectedAccount.username}</p>
        </div>
      )}

      <div className="text-center">
        <p className="text-gray-600 mb-6">
          Send a test email to verify your configuration. The test email will be <NAME_EMAIL>
        </p>
        
        <button
          onClick={handleSendTestEmail}
          disabled={sendingTest}
          className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed mx-auto"
        >
          {sendingTest ? (
            <>
              <TbLoader className="h-5 w-5 animate-spin" />
              Sending Test Email...
            </>
          ) : (
            <>
              <TbSend className="h-5 w-5" />
              Send Test Email
            </>
          )}
        </button>
      </div>
    </div>
  );

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Microsoft Outlook Setup
            </h2>
            <p className="text-gray-600 mt-1">
              Configure your Outlook account for sending invoices
            </p>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className={`flex items-center gap-2 ${currentStep === 'auth' ? 'text-blue-600' : currentStep === 'accounts' || currentStep === 'test' ? 'text-green-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'auth' ? 'bg-blue-100' : currentStep === 'accounts' || currentStep === 'test' ? 'bg-green-100' : 'bg-gray-100'}`}>
              {currentStep === 'accounts' || currentStep === 'test' ? <TbCheck className="h-5 w-5" /> : '1'}
            </div>
            <span className="text-sm font-medium">Authenticate</span>
          </div>
          <div className={`flex items-center gap-2 ${currentStep === 'accounts' ? 'text-blue-600' : currentStep === 'test' ? 'text-green-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'accounts' ? 'bg-blue-100' : currentStep === 'test' ? 'bg-green-100' : 'bg-gray-100'}`}>
              {currentStep === 'test' ? <TbCheck className="h-5 w-5" /> : '2'}
            </div>
            <span className="text-sm font-medium">Select Account</span>
          </div>
          <div className={`flex items-center gap-2 ${currentStep === 'test' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'test' ? 'bg-blue-100' : 'bg-gray-100'}`}>
              3
            </div>
            <span className="text-sm font-medium">Test Email</span>
          </div>
        </div>
      </div>

      {/* Step Content */}
      {currentStep === 'auth' && renderAuthStep()}
      {currentStep === 'accounts' && renderAccountsStep()}
      {currentStep === 'test' && renderTestStep()}
    </motion.div>
  );
};

export default OutlookAuth;
