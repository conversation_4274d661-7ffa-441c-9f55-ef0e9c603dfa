import { motion } from "framer-motion";
import { useState } from "react";
import { toast } from "react-hot-toast";
import { TbArrowLeft, TbMail, TbSettings, TbCheck } from "react-icons/tb";
import OutlookAuth from "./OutlookAuth";

const EmailSettings = ({ onBack, variants }) => {
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [showOutlookAuth, setShowOutlookAuth] = useState(false);

  const emailProviders = [
    {
      id: 'outlook',
      name: 'Microsoft Outlook',
      description: 'Send emails using your Microsoft Outlook account',
      icon: '📧',
      available: true,
      color: 'blue'
    },
    {
      id: 'gmail',
      name: 'Gmail',
      description: 'Send emails using your Gmail account (Coming Soon)',
      icon: '✉️',
      available: false,
      color: 'red'
    }
  ];

  const handleProviderSelect = (provider) => {
    if (!provider.available) {
      toast.error('This provider is not available yet');
      return;
    }

    setSelectedProvider(provider);
    
    if (provider.id === 'outlook') {
      setShowOutlookAuth(true);
    }
  };

  const handleOutlookAuthBack = () => {
    setShowOutlookAuth(false);
    setSelectedProvider(null);
  };

  if (showOutlookAuth) {
    return (
      <OutlookAuth 
        onBack={handleOutlookAuthBack}
        variants={variants}
      />
    );
  }

  return (
    <motion.div
      className="flex flex-col gap-6"
      variants={variants}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <motion.button
            onClick={onBack}
            className="w-12 h-12 rounded-full flex items-center justify-center bg-blue-600 text-white shadow-md hover:bg-blue-700 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            aria-label="Go back"
          >
            <TbArrowLeft className="h-6 w-6" />
          </motion.button>
          <div>
            <h2 className="text-xl font-medium text-gray-700">
              Email Settings
            </h2>
            <p className="text-gray-600 mt-1">
              Configure your email provider to send invoices directly to customers
            </p>
          </div>
        </div>
      </div>

      {/* Email Provider Selection */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <div className="flex items-center gap-3 mb-6">
          <TbSettings className="h-6 w-6 text-blue-600" />
          <h3 className="text-lg font-medium text-gray-900">
            Choose Email Provider
          </h3>
        </div>

        <div className="grid gap-4">
          {emailProviders.map((provider) => (
            <motion.div
              key={provider.id}
              className={`
                relative p-4 border-2 rounded-lg cursor-pointer transition-all
                ${provider.available 
                  ? `border-gray-200 hover:border-${provider.color}-300 hover:bg-${provider.color}-50` 
                  : 'border-gray-100 bg-gray-50 cursor-not-allowed opacity-60'
                }
                ${selectedProvider?.id === provider.id ? `border-${provider.color}-500 bg-${provider.color}-50` : ''}
              `}
              onClick={() => handleProviderSelect(provider)}
              whileHover={provider.available ? { scale: 1.02 } : {}}
              whileTap={provider.available ? { scale: 0.98 } : {}}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="text-3xl">{provider.icon}</div>
                  <div>
                    <h4 className="font-medium text-gray-900 flex items-center gap-2">
                      {provider.name}
                      {!provider.available && (
                        <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                          Coming Soon
                        </span>
                      )}
                    </h4>
                    <p className="text-sm text-gray-600 mt-1">
                      {provider.description}
                    </p>
                  </div>
                </div>
                
                {provider.available && (
                  <div className={`
                    w-6 h-6 rounded-full border-2 flex items-center justify-center
                    ${selectedProvider?.id === provider.id 
                      ? `border-${provider.color}-500 bg-${provider.color}-500` 
                      : `border-gray-300`
                    }
                  `}>
                    {selectedProvider?.id === provider.id && (
                      <TbCheck className="h-4 w-4 text-white" />
                    )}
                  </div>
                )}
              </div>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Information Section */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div className="flex items-start gap-3">
          <TbMail className="h-6 w-6 text-blue-600 mt-0.5" />
          <div>
            <h3 className="font-medium text-blue-900 mb-2">
              How Email Integration Works
            </h3>
            <ul className="text-sm text-blue-800 space-y-2">
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>Connect your email account securely using OAuth authentication</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>Send test emails to verify your configuration</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>Your credentials are stored securely and never shared</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>You can revoke access at any time from your email provider settings</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default EmailSettings;
