// lib/msal/config.js

export const msalConfig = {
  auth: {
    clientId: process.env.NEXT_PUBLIC_AZURE_MS_GRAPH_CLIENT_ID,
    authority: `https://login.microsoftonline.com/${process.env.NEXT_PUBLIC_AZURE_MS_GRAPH_TENANT_ID}`,  // or "organizations"
    redirectUri: process.env.NEXT_PUBLIC_AZURE_MS_GRAPH_REDIRECT_URI,
  },
  cache: { cacheLocation: "sessionStorage", storeAuthStateInCookie: false },
};

export const loginRequest = {
  scopes: [
    process.env.NEXT_PUBLIC_AZURE_MS_GRAPH_BACKEND_API_SCOPE,
    "https://graph.microsoft.com/Mail.Send"
  ],
  prompt: "consent",
};

// Separate request for just Graph API scopes
export const graphLoginRequest = {
  scopes: [
    "https://graph.microsoft.com/Mail.Send",
    "https://graph.microsoft.com/User.Read",
    "https://graph.microsoft.com/Mail.ReadWrite"
  ],
  prompt: "consent",
};